inboundShortMessage:
  name: Student Central Messaging Flow
  description: "Student Central Messaging Flow (PCC: acg-purecloud-customer-service-supervisor)"
  division: Student Central Hubs
  startUpRef: "/inboundShortMessage/states/state[Initial State_11]"
  defaultLanguage: en-au
  supportedLanguages:
    en-au:
      defaultLanguageSkill:
        lit:
          name: English - Chat
  variables:
    - booleanVariable:
        name: Flow.AccountEnabled
        initialValue:
          lit: false
        isOutput: false
    - stringCollectionVariable:
        name: Flow.AccountNotes
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.AccountType
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.AuthResult
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.Campus
        initialValue:
          noValue: true
        isOutput: false
    - stringCollectionVariable:
        name: Flow.Courses
        initialValue:
          noValue: true
        isOutput: false
    - integerVariable:
        name: Flow.DayOfWeek
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.DisplayName
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.DisplayNameSplit
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.EmailMessage
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.ExtensionAttribute1
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.ExtensionAttribute2
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.ExtensionAttribute3
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.Faculties
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.Faculty
        initialValue:
          noValue: true
        isOutput: false
    - integerVariable:
        name: Flow.FacultyGroupIndex
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.FlowJourney
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.FlowLog
        initialValue:
          noValue: true
        isOutput: false
    - booleanVariable:
        name: Flow.HasITAccount
        initialValue:
          lit: false
        isOutput: false
    - stringVariable:
        name: Flow.Id
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.IdentifiedBy
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.IDNumberMessage
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.Interval60
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.IsCloudStudent
        initialValue:
          noValue: true
        isOutput: false
    - booleanVariable:
        name: Flow.IsDev
        initialValue:
          noValue: true
        isOutput: false
    - booleanVariable:
        name: Flow.IsExecutive
        initialValue:
          lit: false
        isOutput: false
    - booleanVariable:
        name: Flow.IsOpen
        initialValue:
          noValue: true
        isOutput: false
    - booleanVariable:
        name: Flow.IsTechnicalGroup
        initialValue:
          lit: false
        isOutput: false
    - booleanVariable:
        name: Flow.IsValidDeakinObject
        initialValue:
          lit: false
        isOutput: false
    - booleanVariable:
        name: Flow.IsVIP
        initialValue:
          lit: false
        isOutput: false
    - booleanVariable:
        name: Flow.IsWeekday
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.IvrName
        initialValue:
          noValue: true
        isOutput: false
    - stringCollectionVariable:
        name: Flow.LockReason
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.LockReasonString
        initialValue:
          noValue: true
        isOutput: false
    - stringCollectionVariable:
        name: Flow.MemberOfGroups
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.NameMessage
        initialValue:
          noValue: true
        isOutput: false
    - integerVariable:
        name: Flow.OverridePriority
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.OverridePriority1
        initialValue:
          noValue: true
        isOutput: false
    - integerVariable:
        name: Flow.OverridePriorityInt
        initialValue:
          noValue: true
        isOutput: false
    - integerVariable:
        name: Flow.PasswordExpiresInDays
        initialValue:
          lit: 0
        isOutput: false
    - stringVariable:
        name: Flow.Phone_Merged
        initialValue:
          noValue: true
        isOutput: false
    - stringCollectionVariable:
        name: Flow.PhoneAdditional
        initialValue:
          noValue: true
        isOutput: false
    - booleanVariable:
        name: Flow.PhoneBridgeGroupsLoopRun1
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.PhoneMobile
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.PhoneNumber
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.PhoneType
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.QueueName
        initialValue:
          noValue: true
        isOutput: false
    - skillVariable:
        name: Flow.RequiredSkill
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.Schools
        initialValue:
          noValue: true
        isOutput: false
    - stringCollectionVariable:
        name: Flow.SchoolsCollection
        initialValue:
          noValue: true
        isOutput: false
    - integerVariable:
        name: Flow.SchoolsGroupIndex
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.SchoolsInFacultyString
        initialValue:
          noValue: true
        isOutput: false
    - integerVariable:
        name: Flow.SchoolsInt
        initialValue:
          noValue: true
        isOutput: false
    - integerVariable:
        name: Flow.SchoolsLoopCount
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.ScriptSet
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.ScriptSetRequiredSkill
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.ServiceNowSysId
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.SipAddress
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.SlotNameAndDOB
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.SlotNameAndEmail
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.SlotReasonForCalling
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.SN_Group_SysID_Dev
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.SN_Group_SysID_Prod
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.SN_GroupName
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.SN_SC_DevEnv
        initialValue:
          noValue: true
        isOutput: false
    - integerVariable:
        name: Flow.TimeOfDay
        initialValue:
          noValue: true
        isOutput: false
    - stringVariable:
        name: Flow.Username
        initialValue:
          noValue: true
        isOutput: false
    - integerVariable:
        name: Flow.UTCOffsetMinutes
        initialValue:
          noValue: true
        isOutput: false
  settingsErrorHandling:
    errorHandling:
      queue:
        targetQueue:
          lit:
            name: Testing Queue - SD Leadership
  states:
    - state:
        name: Initial State
        refId: Initial State_11
        variables:
          - stringVariable:
              name: State.ClosureMessage
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - stringVariable:
              name: State.DeakinIDEntered
              initialValue:
                lit: ""
              isInput: false
              isOutput: false
          - stringVariable:
              name: State.Greeting
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - stringVariable:
              name: State.IDNumber
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - stringVariable:
              name: State.MessageOfTheDay1
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - stringVariable:
              name: State.MessageOfTheDay2
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - stringVariable:
              name: State.Name
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - stringVariable:
              name: State.ReasonForCalling
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - stringVariable:
              name: State.SessionIDNumber
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - stringVariable:
              name: State.TransferMessage
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - stringVariable:
              name: State.WelcomePrivacy
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
        actions:
          - getParticipantData:
              name: Get Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.ScriptSet
                    variable: Flow.ScriptSet
                - attribute:
                    name:
                      lit: Flow.AuthResult
                    variable: Flow.AuthResult
          - updateData:
              name: Update FlowLog, Flow.SN_SC_DevEnv
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n, \"*** Start of SC Message flow..\"\n,\"\\n\"\n)"
                - string:
                    variable: Flow.FlowJourney
                    value:
                      exp: Append(Flow.FlowJourney, "***Start Message Flow - ")
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
                - attribute:
                    name:
                      lit: Flow.FlowJourney
                    value:
                      exp: Flow.FlowJourney
          - dataTableLookup:
              name: Data Table Lookup
              lookupValue:
                lit: standard
              dataTable:
                Student Central Messages:
                  foundOutputs:
                    WelcomePrivacy:
                      var: State.WelcomePrivacy
                    Greeting:
                      var: State.Greeting
                    MessageOfTheDay1:
                      var: State.MessageOfTheDay1
                    MessageOfTheDay2:
                      var: State.MessageOfTheDay2
                    NameAndDOB:
                      noValue: true
                    IDNumber:
                      var: State.IDNumber
                    NameAndEmail:
                      noValue: true
                    ReasonForCalling:
                      var: State.ReasonForCalling
                    TransferToAdviser:
                      var: State.TransferMessage
                    ClosureMessage:
                      var: State.ClosureMessage
                    Spare1:
                      noValue: true
                    Spare2:
                      noValue: true
                    Spare3:
                      noValue: true
                    Spare4:
                      noValue: true
                    Spare5:
                      noValue: true
                    SpareBoolean:
                      noValue: true
                  failureOutputs:
                    errorType:
                      noValue: true
                    errorMessage:
                      noValue: true
              outputs:
                found:
                  actions:
                    - updateData:
                        name: Update FlowLog, Flow.SN_SC_DevEnv
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: "Append(Flow.FlowLog\n, \"Data table success.\"\n,\"\\n\"\n)"
                notFound:
                  actions:
                    - updateData:
                        name: Update FlowLog, Flow.SN_SC_DevEnv
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: "Append(Flow.FlowLog\n, \"Data table not found.\"\n,\"\\n\"\n)"
                failure:
                  actions:
                    - updateData:
                        name: Update FlowLog, Flow.SN_SC_DevEnv
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: "Append(Flow.FlowLog\n, \"Data table failure.\"\n,\"\\n\"\n)"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - updateData:
              name: Update FlowLog, Flow.SN_SC_DevEnv
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n, \"Start of Schedule evaluation.\"\n,\"\\n\"\n)"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - evaluateScheduleGroup:
              name: Evaluate Schedule Group
              inServiceSchedules:
                noValue: true
              evaluate:
                now: true
              scheduleGroup:
                lit:
                  name: Student Central - Message Schedule
                name: Student Central - Message Schedule
              emergencyGroup:
                noValue: true
              outputs:
                open:
                  actions:
                    - updateData:
                        name: "PCC: SCChatOpen"
                        statements:
                          - boolean:
                              variable: Flow.IsOpen
                              value:
                                lit: true
                closed:
                  actions:
                    - updateData:
                        name: "PCC: SCChatOpen"
                        statements:
                          - boolean:
                              variable: Flow.IsOpen
                              value:
                                lit: false
                holiday:
                  actions:
                    - updateData:
                        name: "PCC: SCChatOpen"
                        statements:
                          - boolean:
                              variable: Flow.IsOpen
                              value:
                                lit: false
                emergency:
                  actions:
                    - updateData:
                        name: "PCC: SCChatOpen"
                        statements:
                          - boolean:
                              variable: Flow.IsOpen
                              value:
                                lit: false
          - decision:
              name: Is the messenger flow open?
              condition:
                exp: Flow.IsOpen
              outputs:
                "no":
                  actions:
                    - sendResponse:
                        name: Send Response State.ClosureMessage
                        messageBody:
                          exp: State.ClosureMessage
                    - disconnect:
                        name: Disconnect
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
                - attribute:
                    name:
                      lit: Flow.FlowJourney
                    value:
                      exp: Flow.FlowJourney
                - attribute:
                    name:
                      lit: Flow.IsOpen
                    value:
                      exp: Flow.IsOpen
          - updateData:
              name: Update FlowLog, Flow.SN_SC_DevEnv
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n, \"CM Production analysis.\"\n,\"\\n\"\n)"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - callCommonModule:
              name: Call Common Module Prod Analysis
              commonModule:
                Production Analysis V2:
                  ver_latestPublished:
                    inputs:
                      Common.MediaType:
                        lit: message
                      Common.CallAni:
                        noValue: true
                      Common.FlowLog_InQueue:
                        noValue: true
                    outputs:
                      Common.IsDev:
                        var: Flow.IsDev
                      Common.IsTechnicalGroup:
                        noValue: true
                      Common.FlowLog_InQueue:
                        noValue: true
          - updateData:
              name: Update Customer Data for Bot
              statements:
                - string:
                    variable: Flow.NameMessage
                    value:
                      exp: State.Name
                - string:
                    variable: Flow.IDNumberMessage
                    value:
                      exp: State.IDNumber
                - string:
                    variable: Flow.EmailMessage
                    value:
                      lit: State.Email
          - updateData:
              name: Update FlowLog, Flow.SN_SC_DevEnv
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n, \"Before IsDev.\"\n,\"\\n\"\n)"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - decision:
              name: Decision
              condition:
                exp: Flow.IsDev
              outputs:
                "yes":
                  actions:
                    - sendResponse:
                        name: Send Response
                        messageBody:
                          exp: "\"Development\""
          - updateData:
              name: Update FlowLog, Flow.SN_SC_DevEnv
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n, \"After IsDev.\"\n,\"\\n\"\n)"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - sendResponse:
              name: Send privacy message
              messageBody:
                exp: State.WelcomePrivacy
          - updateData:
              name: Update FlowLog, Flow.SN_SC_DevEnv
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n, \"Before Bot.\"\n,\"\\n\"\n)"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - callBotFlow:
              name: Call Bot Flow
              inputText:
                noValue: true
              exitReason:
                noValue: true
              intent:
                noValue: true
              botFlow:
                1 - Student Central Bot - Get Details:
                  ver_latestPublished:
                    inputs:
                      Slot.IDNumber:
                        noValue: true
                      Slot.NameAndDOB:
                        noValue: true
                      Slot.NameAndEmail:
                        noValue: true
                      Slot.ReasonForCalling:
                        noValue: true
                    outputs:
                      Slot.IDNumber:
                        var: State.SessionIDNumber
                      Slot.NameAndDOB:
                        var: Flow.SlotNameAndDOB
                      Slot.NameAndEmail:
                        var: Flow.SlotNameAndEmail
                      Slot.ReasonForCalling:
                        var: Flow.SlotReasonForCalling
          - updateData:
              name: Update FlowLog, Flow.SN_SC_DevEnv
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n, \"After Bot.\"\n,\"\\n\"\n)"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - updateData:
              name: Update flow journey post Bot 1
              statements:
                - string:
                    variable: Flow.FlowJourney
                    value:
                      exp: Append(Flow.FlowJourney, "Bot 1 complete - ")
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
                - attribute:
                    name:
                      lit: Flow.FlowJourney
                    value:
                      exp: Flow.FlowJourney
                - attribute:
                    name:
                      lit: Flow.SlotNameAndDOB
                    value:
                      exp: Flow.SlotNameAndDOB
                - attribute:
                    name:
                      lit: Flow.SlotNameAndEmail
                    value:
                      exp: Flow.SlotNameAndEmail
                - attribute:
                    name:
                      lit: Flow.SlotReasonForCalling
                    value:
                      exp: Flow.SlotReasonForCalling
          - updateData:
              name: Update FlowLog, Flow.SN_SC_DevEnv
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n, \"Before GetObjectByID.\"\n,\"\\n\"\n)"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - callData:
              name: Get Object by ID V110
              timeout:
                lit:
                  minutes: 1
              category:
                Web Services Data Actions - Deakin:
                  dataAction:
                    GetDeakinObjectById-v110:
                      inputs:
                        IdNumber:
                          exp: If(IsSet(State.SessionIDNumber),State.SessionIDNumber,Flow.Id)
                        Identity:
                          lit: "+***********"
                        ConversationId:
                          exp: Message.ConversationId
                        Context:
                          lit: Message Id Lookup
                      successOutputs:
                        Id:
                          var: Flow.Id
                        Username:
                          var: Flow.Username
                        DisplayName:
                          var: Flow.DisplayName
                        AccountType:
                          var: Flow.AccountType
                        Campus:
                          var: Flow.Campus
                        PhoneNumber:
                          var: Flow.PhoneNumber
                        PhoneMobile:
                          var: Flow.PhoneMobile
                        PhoneAdditional:
                          var: Flow.PhoneAdditional
                        Email:
                          var: Flow.EmailMessage
                        SipAddress:
                          var: Flow.SipAddress
                        AccountEnabled:
                          var: Flow.AccountEnabled
                        LockReason:
                          var: Flow.LockReason
                        PasswordExpiresInDays:
                          var: Flow.PasswordExpiresInDays
                        Courses:
                          var: Flow.Courses
                        PhoneType:
                          var: Flow.PhoneType
                        ServiceNowSysId:
                          var: Flow.ServiceNowSysId
                        IsValidDeakinObject:
                          var: Flow.IsValidDeakinObject
                        IsVIP:
                          var: Flow.IsVIP
                        IsExecutive:
                          var: Flow.IsExecutive
                        IsTechnicalGroup:
                          var: Flow.IsTechnicalGroup
                        HasITAccount:
                          var: Flow.HasITAccount
                        TimeOfDay:
                          var: Flow.TimeOfDay
                        DayOfWeek:
                          var: Flow.DayOfWeek
                        UtcOffsetMinutes:
                          var: Flow.UTCOffsetMinutes
                        IsWeekday:
                          var: Flow.IsWeekday
                        IvrName:
                          var: Flow.IvrName
                        MemberOfGroups:
                          var: Flow.MemberOfGroups
                        AccountNotes:
                          var: Flow.AccountNotes
                        ExtensionAttribute1:
                          var: Flow.ExtensionAttribute1
                        ExtensionAttribute2:
                          var: Flow.ExtensionAttribute2
                        ExtensionAttribute3:
                          var: Flow.ExtensionAttribute3
                      failureOutputs:
                        errorCode:
                          noValue: true
                        status:
                          noValue: true
                        correlationId:
                          noValue: true
                        entityId:
                          noValue: true
                        entityName:
                          noValue: true
                        userMessage:
                          noValue: true
                        userParamsMessage:
                          noValue: true
                        userParams.key:
                          noValue: true
                        userParams.value:
                          noValue: true
                        details.errorCode:
                          noValue: true
                        details.fieldName:
                          noValue: true
                        details.entityId:
                          noValue: true
                        details.entityName:
                          noValue: true
              outputs:
                success:
                  actions:
                    - setParticipantData:
                        name: Set Call Bridge Data
                        attributes:
                          - attribute:
                              name:
                                lit: Flow.Id
                              value:
                                exp: Flow.Id
                          - attribute:
                              name:
                                lit: Flow.Username
                              value:
                                exp: Flow.Username
                          - attribute:
                              name:
                                lit: Flow.DisplayName
                              value:
                                exp: Flow.DisplayName
                          - attribute:
                              name:
                                lit: Flow.AccountType
                              value:
                                exp: Flow.AccountType
                          - attribute:
                              name:
                                lit: Flow.Campus
                              value:
                                exp: Flow.Campus
                          - attribute:
                              name:
                                lit: Flow.PhoneNumber
                              value:
                                exp: Flow.PhoneNumber
                          - attribute:
                              name:
                                lit: Flow.PhoneMobile
                              value:
                                exp: Flow.PhoneMobile
                          - attribute:
                              name:
                                lit: Flow.PhoneAdditional
                              value:
                                exp: Flow.PhoneAdditional
                          - attribute:
                              name:
                                lit: Flow.Email
                              value:
                                exp: Flow.EmailMessage
                          - attribute:
                              name:
                                lit: Flow.SipAddress
                              value:
                                exp: Flow.SipAddress
                          - attribute:
                              name:
                                lit: Flow.AccountEnabled
                              value:
                                exp: Flow.AccountEnabled
                          - attribute:
                              name:
                                lit: Flow.LockReason
                              value:
                                exp: Flow.LockReason
                          - attribute:
                              name:
                                lit: Flow.PasswordExpiresInDays
                              value:
                                exp: Flow.PasswordExpiresInDays
                          - attribute:
                              name:
                                lit: Flow.Courses
                              value:
                                exp: Flow.Courses
                          - attribute:
                              name:
                                lit: Flow.PhoneType
                              value:
                                exp: Flow.PhoneType
                          - attribute:
                              name:
                                lit: Flow.ServiceNowSysId
                              value:
                                exp: Flow.ServiceNowSysId
                          - attribute:
                              name:
                                lit: Flow.IsValidDeakinObject
                              value:
                                exp: Flow.IsValidDeakinObject
                          - attribute:
                              name:
                                lit: Flow.IsVIP
                              value:
                                exp: Flow.IsVIP
                          - attribute:
                              name:
                                lit: Flow.IsExecutive
                              value:
                                exp: Flow.IsExecutive
                          - attribute:
                              name:
                                lit: Flow.IsTechnicalGroup
                              value:
                                exp: Flow.IsTechnicalGroup
                          - attribute:
                              name:
                                lit: Flow.HasITAccount
                              value:
                                exp: Flow.HasITAccount
                          - attribute:
                              name:
                                lit: Flow.TimeOfDay
                              value:
                                exp: Flow.TimeOfDay
                          - attribute:
                              name:
                                lit: Flow.DayOfWeek
                              value:
                                exp: Flow.DayOfWeek
                          - attribute:
                              name:
                                lit: Flow.UTCOffsetMinutes
                              value:
                                exp: Flow.UTCOffsetMinutes
                          - attribute:
                              name:
                                lit: Flow.IsWeekday
                              value:
                                exp: Flow.IsWeekday
                          - attribute:
                              name:
                                lit: Flow.IvrName
                              value:
                                exp: Flow.IvrName
                          - attribute:
                              name:
                                lit: Flow.AccountNotes
                              value:
                                exp: Flow.AccountNotes
                          - attribute:
                              name:
                                lit: Flow.ExtensionAttribute1
                              value:
                                exp: Flow.ExtensionAttribute1
                          - attribute:
                              name:
                                lit: Flow.ExtensionAttribute2
                              value:
                                exp: Flow.ExtensionAttribute2
                          - attribute:
                              name:
                                lit: Flow.ExtensionAttribute3
                              value:
                                exp: Flow.ExtensionAttribute3
                    - setParticipantData:
                        name: Set other Data
                        attributes:
                          - attribute:
                              name:
                                lit: Flow.LockReasonString
                              value:
                                exp: Flow.LockReasonString
                          - attribute:
                              name:
                                lit: Flow.IvrName
                              value:
                                exp: Flow.IvrName
                          - attribute:
                              name:
                                lit: Flow.IdentifiedBy
                              value:
                                exp: Flow.IdentifiedBy
                          - attribute:
                              name:
                                lit: Flow.Interval60
                              value:
                                exp: Flow.Interval60
                    - updateData:
                        name: Update Data
                        statements:
                          - string:
                              variable: Flow.DisplayNameSplit
                              value:
                                exp: GetAt(MakeList(Flow.DisplayName),0)
                    - updateData:
                        name: Update FlowLog, Flow.SN_SC_DevEnv
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: "Append(Flow.FlowLog\n, \"Get ObjectById success path.\"\n,\"\\n\"\n)"
                    - setParticipantData:
                        name: Set Participant Data
                        attributes:
                          - attribute:
                              name:
                                lit: Flow.FlowLog
                              value:
                                exp: Flow.FlowLog
                failure:
                  actions:
                    - updateData:
                        name: Update Flow log
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: Append(Flow.FlowLog,"\nID bridge failed from ",State.DeakinIDEntered,".")
                    - callData:
                        name: Report Bridge Action
                        timeout:
                          lit:
                            minutes: 1
                        category:
                          Web Services Data Actions - Deakin:
                            dataAction:
                              ReportBridgeActionEvent-v110:
                                inputs:
                                  Identity:
                                    exp: Message.Message.id
                                  ConversationId:
                                    exp: Message.ConversationId
                                  Context:
                                    lit: Deakin Messaging Flow with Bots
                                  Message:
                                    exp: Flow.FlowLog
                                  ResetStatusCounters:
                                    noValue: true
                                successOutputs:
                                  RequestsProcessed:
                                    noValue: true
                                  SlowRequestsProcessed:
                                    noValue: true
                                  ExceptionCount:
                                    noValue: true
                                  LastException:
                                    noValue: true
                                  BridgeActionEvents:
                                    noValue: true
                                  ProcessStartTime:
                                    noValue: true
                                  DataLastRefreshedAt:
                                    noValue: true
                                  DataLastModified:
                                    noValue: true
                                  DataLastRefreshMessage:
                                    noValue: true
                                  DataLastRefreshTimeTaken:
                                    noValue: true
                                  DataRecordCount:
                                    noValue: true
                                  Version:
                                    noValue: true
                                  PID:
                                    noValue: true
                                failureOutputs:
                                  errorCode:
                                    noValue: true
                                  status:
                                    noValue: true
                                  correlationId:
                                    noValue: true
                                  entityId:
                                    noValue: true
                                  entityName:
                                    noValue: true
                                  userMessage:
                                    noValue: true
                                  userParamsMessage:
                                    noValue: true
                                  userParams.key:
                                    noValue: true
                                  userParams.value:
                                    noValue: true
                                  details.errorCode:
                                    noValue: true
                                  details.fieldName:
                                    noValue: true
                                  details.entityId:
                                    noValue: true
                                  details.entityName:
                                    noValue: true
                        outputs:
                          failure:
                            actions:
                              - updateData:
                                  name: Update Flow log
                                  statements:
                                    - string:
                                        variable: Flow.FlowLog
                                        value:
                                          exp: Append(Flow.FlowLog,"\nID bridge failed from ",State.DeakinIDEntered,".")
                          timeout:
                            actions:
                              - updateData:
                                  name: Update Flow log
                                  statements:
                                    - string:
                                        variable: Flow.FlowLog
                                        value:
                                          exp: Append(Flow.FlowLog,"\nID bridge timedout from ",State.DeakinIDEntered,".")
                timeout:
                  actions:
                    - updateData:
                        name: Update Flow log
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: Append(Flow.FlowLog,"\nID bridge timed out from ",State.DeakinIDEntered,".")
                    - callData:
                        name: Report Bridge Action
                        timeout:
                          lit:
                            minutes: 1
                        category:
                          Web Services Data Actions - Deakin:
                            dataAction:
                              ReportBridgeActionEvent-v110:
                                inputs:
                                  Identity:
                                    exp: Message.Message.id
                                  ConversationId:
                                    exp: Message.ConversationId
                                  Context:
                                    lit: Deakin Messaging Flow with Bots
                                  Message:
                                    exp: Flow.FlowLog
                                  ResetStatusCounters:
                                    noValue: true
                                successOutputs:
                                  RequestsProcessed:
                                    noValue: true
                                  SlowRequestsProcessed:
                                    noValue: true
                                  ExceptionCount:
                                    noValue: true
                                  LastException:
                                    noValue: true
                                  BridgeActionEvents:
                                    noValue: true
                                  ProcessStartTime:
                                    noValue: true
                                  DataLastRefreshedAt:
                                    noValue: true
                                  DataLastModified:
                                    noValue: true
                                  DataLastRefreshMessage:
                                    noValue: true
                                  DataLastRefreshTimeTaken:
                                    noValue: true
                                  DataRecordCount:
                                    noValue: true
                                  Version:
                                    noValue: true
                                  PID:
                                    noValue: true
                                failureOutputs:
                                  errorCode:
                                    noValue: true
                                  status:
                                    noValue: true
                                  correlationId:
                                    noValue: true
                                  entityId:
                                    noValue: true
                                  entityName:
                                    noValue: true
                                  userMessage:
                                    noValue: true
                                  userParamsMessage:
                                    noValue: true
                                  userParams.key:
                                    noValue: true
                                  userParams.value:
                                    noValue: true
                                  details.errorCode:
                                    noValue: true
                                  details.fieldName:
                                    noValue: true
                                  details.entityId:
                                    noValue: true
                                  details.entityName:
                                    noValue: true
                        outputs:
                          failure:
                            actions:
                              - updateData:
                                  name: Update Flow log
                                  statements:
                                    - string:
                                        variable: Flow.FlowLog
                                        value:
                                          exp: Append(Flow.FlowLog,"\nID bridge failed from ",State.DeakinIDEntered,".")
                          timeout:
                            actions:
                              - updateData:
                                  name: Update Flow log
                                  statements:
                                    - string:
                                        variable: Flow.FlowLog
                                        value:
                                          exp: Append(Flow.FlowLog,"\nID bridge timedout from ",State.DeakinIDEntered,".")
          - updateData:
              name: Update FlowLog, Flow.SN_SC_DevEnv
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n, \"After Get ObjectById.\"\n,\"\\n\"\n)"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - updateData:
              name: Update Vars
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: Append(Flow.FlowLog, "Starting groups loop in ID Task ", "\n")
                - string:
                    variable: Flow.Faculties
                    value:
                      lit: ""
                - string:
                    variable: Flow.Faculty
                    value:
                      lit: ""
                - string:
                    variable: Flow.ScriptSetRequiredSkill
                    value:
                      lit: ""
                - string:
                    variable: Flow.IsCloudStudent
                    value:
                      lit: "false"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - switch:
              name: Flow.AccountType=="STUDENT"
              evaluate:
                firstTrue:
                  default:
                    actions:
                      - updateData:
                          name: Update Flow.FlowLog
                          statements:
                            - string:
                                variable: Flow.FlowLog
                                value:
                                  exp: Append(Flow.FlowLog, "Skipping groups loop as not STUDENT", "\n")
                      - setParticipantData:
                          name: Set Participant Data
                          attributes:
                            - attribute:
                                name:
                                  lit: Flow.FlowLog
                                value:
                                  exp: Flow.FlowLog
                  cases:
                    - case:
                        value:
                          exp: Flow.AccountType=="STUDENT"
                        actions:
                          - updateData:
                              name: Update Vars
                              statements:
                                - string:
                                    variable: Flow.FlowLog
                                    value:
                                      exp: Append(Flow.FlowLog, "Starting groups loop in ID Task ", "\n")
                                - string:
                                    variable: Flow.Faculties
                                    value:
                                      lit: ""
                                - string:
                                    variable: Flow.Schools
                                    value:
                                      lit: ""
                                - string:
                                    variable: Flow.Faculty
                                    value:
                                      lit: ""
                          - updateData:
                              name: Update Data
                              statements:
                                - integer:
                                    variable: Flow.FacultyGroupIndex
                                    value:
                                      lit: 1
                                - integer:
                                    variable: Flow.SchoolsGroupIndex
                                    value:
                                      lit: 0
                          - loop:
                              name: Loop
                              currentIndex:
                                var: Flow.FacultyGroupIndex
                              loopCount:
                                exp: Count(Flow.MemberOfGroups)
                              outputs:
                                loop:
                                  actions:
                                    - decision:
                                        name: Decision
                                        condition:
                                          exp: Contains(GetAt(Flow.MemberOfGroups,Flow.FacultyGroupIndex),"wgf-")
                                        outputs:
                                          "yes":
                                            actions:
                                              - updateData:
                                                  name: Update Data
                                                  statements:
                                                    - string:
                                                        variable: Flow.Faculties
                                                        value:
                                                          exp: "Append(\n  Flow.Faculties,\n  \"{\",\n  Substring(\n    GetAt(\n      Flow.MemberOfGroups,\n      Flow.FacultyGroupIndex\n    ),\n    4,\n    FindString(\n      GetAt(\n        Flow.MemberOfGroups,\n        Flow.FacultyGroupIndex\n      ),\n\t  \";\"\n    )-4\n  ),\n  \"}\"\n)"
                          - loop:
                              name: Loop
                              currentIndex:
                                var: Flow.SchoolsGroupIndex
                              loopCount:
                                exp: Count(Flow.MemberOfGroups)
                              outputs:
                                loop:
                                  actions:
                                    - decision:
                                        name: Decision
                                        condition:
                                          exp: Contains(GetAt(Flow.MemberOfGroups,Flow.SchoolsGroupIndex),"wgsch-")
                                        outputs:
                                          "yes":
                                            actions:
                                              - updateData:
                                                  name: Update Data
                                                  statements:
                                                    - string:
                                                        variable: Flow.Schools
                                                        value:
                                                          exp: "Append(\n  Flow.Schools,\n  Substring(\n    GetAt(\n      Flow.MemberOfGroups,\n      Flow.SchoolsGroupIndex\n    ),\n    6,\n    FindString(\n      GetAt(\n        Flow.MemberOfGroups,\n        Flow.SchoolsGroupIndex\n      ),\n\t  \";\"\n    )-6\n  ),\n  \",\"\n)"
                          - decision:
                              name: Decision
                              condition:
                                exp: IsNotSetOrEmpty(Flow.Schools)
                              outputs:
                                "yes":
                                  actions:
                                    - updateData:
                                        name: Update Data
                                        statements:
                                          - string:
                                              variable: Flow.Schools
                                              value:
                                                lit: No schools identified.
                                    - setParticipantData:
                                        name: Set Participant Data
                                        attributes:
                                          - attribute:
                                              name:
                                                lit: Flow.Faculties
                                              value:
                                                exp: Flow.Faculties
                                          - attribute:
                                              name:
                                                lit: Flow.IsCloudStudent
                                              value:
                                                exp: Flow.IsCloudStudent
                                          - attribute:
                                              name:
                                                lit: Flow.Schools
                                              value:
                                                exp: Flow.Schools
                                          - attribute:
                                              name:
                                                lit: Flow.RequiredSkill
                                              value:
                                                exp: Flow.RequiredSkill
                                          - attribute:
                                              name:
                                                lit: Flow.SchoolsInFacultyString
                                              value:
                                                lit: No schools identified from faculty
                                "no":
                                  actions:
                                    - updateData:
                                        name: Update Data
                                        statements:
                                          - string:
                                              variable: Flow.IsCloudStudent
                                              value:
                                                exp: FindFirst(Flow.MemberOfGroups,"wg-all-students-cloud;_ALL Students Cloud")!=-1
                                          - string:
                                              variable: Flow.FlowLog
                                              value:
                                                exp: Append(Flow.FlowLog, "groups logic completed in phone bridge ", Flow.Faculties ,"\n")
                                          - boolean:
                                              variable: Flow.PhoneBridgeGroupsLoopRun1
                                              value:
                                                lit: true
                                          - stringCollection:
                                              variable: Flow.SchoolsCollection
                                              value:
                                                exp: Split(Flow.Schools,",")
                                          - integer:
                                              variable: Flow.SchoolsInt
                                              value:
                                                lit: 0
                                          - string:
                                              variable: Flow.SchoolsInFacultyString
                                              value:
                                                lit: ""
                                    - loop:
                                        name: Loop
                                        currentIndex:
                                          var: Flow.SchoolsLoopCount
                                        loopCount:
                                          exp: Count(Flow.SchoolsCollection)
                                        outputs:
                                          loop:
                                            actions:
                                              - decision:
                                                  name: Decision
                                                  condition:
                                                    exp: FindString(GetAt(Flow.SchoolsCollection,Flow.SchoolsLoopCount),Substring(Flow.Faculties,1,2))==0
                                                  outputs:
                                                    "yes":
                                                      actions:
                                                        - updateData:
                                                            name: Update Data
                                                            statements:
                                                              - string:
                                                                  variable: Flow.SchoolsInFacultyString
                                                                  value:
                                                                    exp: "Append(\n  Flow.SchoolsInFacultyString,\n  \",\",\n    GetAt(\n      Flow.SchoolsCollection,\n      Flow.SchoolsLoopCount\n    )\n)"
                                    - setParticipantData:
                                        name: Set Participant Data
                                        attributes:
                                          - attribute:
                                              name:
                                                lit: Flow.Faculties
                                              value:
                                                exp: Flow.Faculties
                                          - attribute:
                                              name:
                                                lit: Flow.IsCloudStudent
                                              value:
                                                exp: Flow.IsCloudStudent
                                          - attribute:
                                              name:
                                                lit: Flow.Schools
                                              value:
                                                exp: Substring(Flow.Schools,0,Length(Flow.Schools)-1)
                                          - attribute:
                                              name:
                                                lit: Flow.RequiredSkill
                                              value:
                                                exp: Flow.RequiredSkill
                                          - attribute:
                                              name:
                                                lit: Flow.SchoolsInFacultyString
                                              value:
                                                exp: Substring(Flow.SchoolsInFacultyString,1,Length(Flow.SchoolsInFacultyString))
          - decision:
              name: Decision
              condition:
                exp: IsNotSetOrEmpty(Flow.Faculty)
              outputs:
                "yes":
                  actions:
                    - updateData:
                        name: Update Data
                        statements:
                          - string:
                              variable: Flow.Faculty
                              value:
                                lit: No Faculty identified
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.Faculty
                    value:
                      exp: Flow.Faculty
          - switch:
              name: Faculties switch
              evaluate:
                firstTrue:
                  default:
                    actions:
                      - decision:
                          name: Decision
                          condition:
                            exp: IsNotSetOrEmpty(Flow.Faculty)
                          outputs:
                            "yes":
                              actions:
                                - updateData:
                                    name: Update Data
                                    statements:
                                      - string:
                                          variable: Flow.Faculty
                                          value:
                                            lit: No Faculty Identified
                  cases:
                    - case:
                        value:
                          exp: Flow.Faculties=="{01}"
                        actions:
                          - updateData:
                              name: Update Data
                              statements:
                                - string:
                                    variable: Flow.Faculty
                                    value:
                                      lit: Faculty of Business and Law
                    - case:
                        value:
                          exp: Flow.Faculties=="{03}"
                        actions:
                          - updateData:
                              name: Update Data
                              statements:
                                - string:
                                    variable: Flow.Faculty
                                    value:
                                      lit: Faculty of Health
                    - case:
                        value:
                          exp: Flow.Faculties=="{04}"
                        actions:
                          - updateData:
                              name: Update Data
                              statements:
                                - string:
                                    variable: Flow.Faculty
                                    value:
                                      lit: Faculty of Arts and Education
                    - case:
                        value:
                          exp: Flow.Faculties=="{05}"
                        actions:
                          - updateData:
                              name: Update Data
                              statements:
                                - string:
                                    variable: Flow.Faculty
                                    value:
                                      lit: Faculty of Science, Engineering and Built Environment
                    - case:
                        value:
                          exp: Flow.Faculties=="{09}"
                    - case:
                        value:
                          exp: Flow.Faculties=="{10}"
                    - case:
                        value:
                          exp: Flow.Faculties=="{17}"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
                - attribute:
                    name:
                      lit: Flow.Faculty
                    value:
                      exp: Flow.Faculty
          - updateData:
              name: Update Data
              statements:
                - string:
                    variable: Flow.OverridePriority1
                    value:
                      exp: Flow.OverridePriorityInt
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.Faculty
                    value:
                      exp: Flow.Faculty
                - attribute:
                    name:
                      lit: ScriptSetRequiredSkill
                    value:
                      exp: Flow.ScriptSetRequiredSkill
                - attribute:
                    name:
                      lit: Flow.OverridePriority
                    value:
                      exp: Flow.OverridePriority1
                - attribute:
                    name:
                      lit: Flow.Phone_Merged
                    value:
                      exp: Flow.Phone_Merged
          - updateData:
              name: Update FlowLog, Flow.SN_SC_DevEnv
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n, \"Before Script\"\n,\"\\n\"\n)"
          - decision:
              name: Check if script was previously set by another queue.
              condition:
                exp: Flow.ScriptSet=="DSA" or Flow.ScriptSet == "Student Central"
              outputs:
                "yes":
                  actions:
                    - updateData:
                        name: Update FlowLog_InQueue
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: "Append(Flow.FlowLog, \n\"ScriptSet is correctly set as: \",Flow.ScriptSet, \n\"\\n\")"
                "no":
                  actions:
                    - updateData:
                        name: Update FlowLog_InQueue
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: "Append(Flow.FlowLog, \n\"ScriptSet is incorrectly set to: \",Flow.ScriptSet, \n\"Starting process to update script for : this flow\",\n\"\\n\")"
                    - updateData:
                        name: Used for queuename for data table. Expand to include school/faculty based information if required in future.
                        statements:
                          - string:
                              variable: Flow.QueueName
                              value:
                                lit: "\"Student Central - General\""
                    - dataTableLookup:
                        name: Data Table Lookup Service Now Group Mapping
                        lookupValue:
                          lit: Student Central - General
                        dataTable:
                          Service Now Group Mapping:
                            foundOutputs:
                              ServiceNow Group Name:
                                var: Flow.SN_GroupName
                              SN Assignment Group Prod:
                                var: Flow.SN_Group_SysID_Prod
                              SN Assignment Group Dev:
                                var: Flow.SN_Group_SysID_Dev
                            failureOutputs:
                              errorType:
                                noValue: true
                              errorMessage:
                                noValue: true
                    - setParticipantData:
                        name: Set Participant Data
                        attributes:
                          - attribute:
                              name:
                                lit: Flow.SN_Group_SysID_Dev
                              value:
                                exp: Flow.SN_Group_SysID_Dev
                          - attribute:
                              name:
                                lit: Flow.SN_Group_SysID_Prod
                              value:
                                exp: Flow.SN_Group_SysID_Prod
                          - attribute:
                              name:
                                lit: Flow.QueueName
                              value:
                                exp: Flow.QueueName
                    - updateData:
                        name: Update FlowLog, Flow.SN_SC_DevEnv
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: "Append(Flow.FlowLog\n, \"Before Setting Script.\"\n,\"\\n\"\n)"
                    - setScreenPop:
                        name: Set Screen Pop Student central Script V2
                        screenPopScript:
                          Student Central Script - v2:
                            inputs:
                              Campus:
                                exp: Flow.Campus
                              LockReason:
                                lit: No data returned from Deakin
                              accountIsLocked:
                                lit: false
                              SNContactType:
                                lit: chat
                              PhoneNumber:
                                exp: Flow.PhoneNumber
                              IsValidDeakinObject:
                                lit: false
                              Inv_IsValidDeakinObject:
                                lit: true
                              isStaff:
                                lit: false
                              CallBackDate:
                                lit: ""
                              SNSys_ID:
                                exp: Flow.ServiceNowSysId
                              showTickets:
                                lit: false
                              CallAni:
                                lit: ""
                              containsMoreInfo:
                                lit: false
                              CustomerDisplayName:
                                exp: Flow.DisplayName
                              Username:
                                exp: Flow.Username
                              Id:
                                exp: Flow.Id
                              PhoneMobile:
                                exp: Flow.PhoneMobile
                              Email:
                                exp: Flow.EmailMessage
                              InScriptBridgeRan:
                                lit: false
                              customerFound:
                                exp: Flow.AccountType == "STUDENT" or Flow.AccountType == "STAFF"
                              passwordHasExpired:
                                lit: false
                              passwordCannotExpire:
                                lit: false
                              Course:
                                lit: ""
                              isStudent:
                                lit: false
                              PhoneType:
                                lit: ""
                              accountHasIssue:
                                lit: false
                              isCallBack:
                                lit: false
                              RequestedCallbackNumber:
                                lit: ""
                              InteractionID:
                                exp: Message.ConversationId
                              ConversationID:
                                exp: Message.ConversationId
                              IdentifiedBy:
                                lit: ""
                              Faculty:
                                lit: ""
                              ServiceNowButtonHidden:
                                lit: true
                              AccountType:
                                lit: ""
                              ScriptSetRequiredSkill:
                                lit: ""
                              ReSkillBool:
                                lit: false
                              SN_AssGroup_SysID:
                                lit: b2cff09cdb5f0c1020053e8f7c9619cf
                              SN_Env:
                                exp: "If(Flow.IsDev, Flow.SN_SC_DevEnv, \"https://deakinesmprod.service-now.com/\")"
                              ProbCaller_AlertComments:
                                lit: ""
                              ProbCaller_AlertMsg:
                                lit: ""
                              ProbCaller_AlertAgent:
                                lit: false
                              AuthResult:
                                exp: Flow.AuthResult
                    - updateData:
                        name: Update Scriptset
                        statements:
                          - string:
                              variable: Flow.ScriptSet
                              value:
                                exp: "\"DSA\""
          - updateData:
              name: Update flow journey exit
              statements:
                - string:
                    variable: Flow.FlowJourney
                    value:
                      exp: Append(Flow.FlowJourney, "Transfer to agent ", "\n")
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
                - attribute:
                    name:
                      lit: Flow.OverridePriority
                    value:
                      exp: Flow.OverridePriority
                - attribute:
                    name:
                      lit: Flow.FlowJourney
                    value:
                      exp: Flow.FlowJourney
          - sendResponse:
              name: Send Response - transfer to adviser
              messageBody:
                exp: State.TransferMessage
          - updateData:
              name: Update FlowLog, Flow.SN_SC_DevEnv
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n, \"Before Priority\"\n,\"\\n\"\n)"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - callCommonModule:
              name: ACD Routing Priority Lookup
              commonModule:
                ACD Routing Priority Lookup:
                  ver_latestPublished:
                    inputs:
                      Common.AccountType:
                        exp: Flow.AccountType
                      Common.DSLDirectRoutingRequired:
                        noValue: true
                      Common.Email_Daily_Communications:
                        noValue: true
                      Common.Email_Handover:
                        noValue: true
                      Common.Email_SOC_Notification:
                        noValue: true
                      Common.Email_Video_Conference_Booking:
                        noValue: true
                      Common.Email_Walk_Up_Appointment:
                        noValue: true
                      Common.IdentifiedBy:
                        lit: Id
                      Common.IsExecutive:
                        exp: Flow.IsExecutive
                      Common.IsInternationalCaller:
                        lit: false
                      Common.IsStaff:
                        exp: Flow.AccountType=="STAFF"
                      Common.IsStudent:
                        exp: Flow.AccountType=="STUDENT"
                      Common.IsTechnicalGroup:
                        exp: Flow.IsTechnicalGroup
                      Common.IsVIP:
                        exp: Flow.IsVIP
                      Common.IvrName:
                        exp: Flow.IvrName
                      Common.MediaType:
                        lit: message
                      Common.OverridePriority:
                        lit: 0
                      Common.PhoneType:
                        exp: Flow.PhoneType
                      Common.SN_taskType:
                        noValue: true
                      Common.ExtensionAttribute1:
                        exp: Flow.ExtensionAttribute1
                      Common.IsCloudStudent:
                        exp: FindFirst(Flow.MemberOfGroups,"wg-all-students-cloud;_ALL Students Cloud")!=-1
                      Common.FlowLog:
                        noValue: true
                    outputs:
                      Common.OverridePriority:
                        var: Flow.OverridePriority
                      Common.FlowLog:
                        noValue: true
                      Common.ErrorType:
                        noValue: true
                      Common.ErrorMessage:
                        noValue: true
          - updateData:
              name: Update FlowLog, Flow.SN_SC_DevEnv
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n, \"Transferring to Student central queue.\"\n,\"\\n\"\n)"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - transferToAcd:
              name: Transfer to Student Central - General
              targetQueue:
                lit:
                  name: Student Central - General
              acdSkills:
                - acdSkill:
                    lit:
                      name: SSN - General
              priority:
                exp: Flow.OverridePriority
              preferredAgents:
                noValue: true
              languageSkill:
                noValue: true
              directAgent:
                noValue: true
